# -*- coding: utf-8 -*-
import logging
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Type, Any, Tuple
import sys
import os
import hashlib
import pickle
import json
import argparse
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import matplotlib.font_manager as fm

# --- Setup Project Path ---
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CORE_CODE_PATH = os.path.join(PROJECT_ROOT, '核心代码')
CONFIG_PATH = os.path.join(PROJECT_ROOT, '配置')
if CORE_CODE_PATH not in sys.path: sys.path.insert(0, CORE_CODE_PATH)
if CONFIG_PATH not in sys.path: sys.path.insert(0, CONFIG_PATH)
sys.path.insert(0, PROJECT_ROOT)

# --- Imports with Error Handling ---
try:
    from 配置.系统配置 import Config
    from 核心代码.市场数据.数据获取器 import convert_ohlcv_to_uppercase, load_local_minute_data
    from 核心代码.交易策略.策略库 import TradingStrategy, STRATEGIES
    from 核心代码.风险管理.风险控制器 import DefaultRiskManager
    from 核心代码.因子计算.因子库 import calculate_factors
    from 核心代码.监控与报告.性能报告器 import calculate_backtest_metrics
    imports_ok = True
except ImportError as e:
    logging.critical(f"无法导入核心模块: {e}. 请确保路径正确。", exc_info=True)
    sys.exit(1)

# --- Logger Setup ---
log_format = "%(asctime)s [%(levelname)s] [%(name)s]: %(message)s"
log_dir = os.path.join(PROJECT_ROOT, '回测结果')
os.makedirs(log_dir, exist_ok=True)
log_file_path = os.path.join(log_dir, 'backtest_engine_v5_final.log')
for handler in logging.root.handlers[:]: logging.root.removeHandler(handler)
logging.basicConfig(level=logging.INFO, format=log_format,
                    handlers=[logging.StreamHandler(sys.stdout),
                              logging.FileHandler(log_file_path, encoding='utf-8', mode='w')])
logger = logging.getLogger("模拟回测引擎_V5_Final")
logging.getLogger('matplotlib').setLevel(logging.WARNING)


# ==============================================================================
# --- Portfolio Class (内置于引擎文件) ---
# ==============================================================================
class Portfolio:
    def __init__(self, config: Any):
        self.config_dict = config.__dict__ if hasattr(config, '__dict__') else config
        self.initial_capital: float = float(self.config_dict.get('initial_cash', 100000.0))
        self.current_cash: float = self.initial_capital
        self.positions: Dict[str, Dict[str, Any]] = {}
        self.trade_log: List[Dict] = []
        self.transaction_cost_pct: float = float(self.config_dict.get('transaction_cost_pct', 0.001))
        logger.info(f"组合管理器(V3)初始化: 初始资金={self.initial_capital:.2f}, 交易成本={self.transaction_cost_pct:.4%}")

    def get_position_size(self, symbol: str) -> float:
        return self.positions.get(symbol, {}).get('shares', 0.0)

    def get_portfolio_value(self, current_prices_snapshot: Optional[pd.Series] = None) -> Dict:
        positions_value = 0.0
        if self.positions and current_prices_snapshot is not None:
            for symbol, pos_details in self.positions.items():
                shares = pos_details.get('shares', 0.0)
                if abs(shares) > 1e-9:
                    current_price = current_prices_snapshot.get(symbol, pos_details.get('avg_price', 0.0))
                    positions_value += shares * current_price
        total_value = self.current_cash + positions_value
        return {'total': total_value, 'cash': self.current_cash, 'positions_value': positions_value}

    def execute_trade(self, signal: Dict):
        symbol, action, price, size, timestamp = signal['symbol'], signal['action'].lower(), signal['price'], signal['size'], signal['timestamp']
        trade_value, commission, realized_pnl = price * size, price * size * self.transaction_cost_pct, 0.0

        if action == 'buy':
            if self.current_cash < trade_value + commission:
                logger.warning(f"[{timestamp}] 现金不足买入 {symbol}，跳过交易。")
                return
            self.current_cash -= (trade_value + commission)
            self.positions[symbol] = {'shares': size, 'avg_price': price, 'stop_loss': signal.get('stop_loss_price'), 'take_profit': signal.get('take_profit_price'), 'entry_timestamp': timestamp}
            log_action = 'buy_to_open'
        elif action == 'sell':
            current_shares = self.get_position_size(symbol)
            if size > current_shares: size = current_shares
            if size <= 1e-9: return
            avg_price = self.positions[symbol]['avg_price']
            realized_pnl = (price - avg_price) * size - commission
            self.current_cash += (price * size - commission)
            del self.positions[symbol]
            log_action = 'sell_to_close'
        else: return

        self.trade_log.append({'timestamp': timestamp, 'symbol': symbol, 'action': log_action, 'price': price, 'size': size, 'commission': commission, 'realized_pnl': realized_pnl, 'signal_type': signal.get('signal_type', 'strategy')})
        logger.info(f"[{timestamp}] 执行交易: {log_action} {size:.4f} {symbol} @{price:.4f}. PnL: {realized_pnl:.2f}. 现金: {self.current_cash:.2f}")

    def monitor_and_execute_sl_tp(self, current_prices: pd.Series, timestamp: pd.Timestamp):
        if not self.positions or current_prices.empty: return
        for symbol, pos_details in list(self.positions.items()):
            shares, stop_loss, take_profit = pos_details.get('shares', 0.0), pos_details.get('stop_loss'), pos_details.get('take_profit')
            if shares <= 1e-9: continue
            current_price = current_prices.get(symbol)
            if pd.isna(current_price) or current_price <= 0: continue
            exit_reason = None
            if stop_loss and current_price <= stop_loss: exit_reason = 'stop_loss'
            elif take_profit and current_price >= take_profit: exit_reason = 'take_profit'
            if exit_reason:
                logger.info(f"[{timestamp}] {symbol} 触发平仓: {exit_reason}! 当前价: {current_price:.4f}, SL: {stop_loss}, TP: {take_profit}")
                self.execute_trade({'symbol': symbol, 'action': 'sell', 'price': current_price, 'size': shares, 'timestamp': timestamp, 'signal_type': f'exit_{exit_reason}'})

# ==============================================================================
# --- Backtester Class ---
# ==============================================================================
class MinuteEventBacktester:
    CACHE_VERSION = "v5.0"

    def __init__(self, config: Config, strategy_class: Type[TradingStrategy], strategy_params: Dict):
        self.config = config
        self.portfolio = Portfolio(config)
        self.risk_manager = DefaultRiskManager(config.__dict__.copy())
        self.symbols = list(set([config.benchmark_symbol] + config.crypto_pairs))
        self.strategy_instance = strategy_class(self, self.symbols, params=strategy_params)
        self.all_historical_data: Optional[pd.DataFrame] = None
        self.current_dt: Optional[pd.Timestamp] = None
        self.equity_curve = []

    def _load_data(self, start_date: str, end_date: str):
        logger.info(f"开始加载和处理数据从 {start_date} 到 {end_date}...")
        cache_dir = os.path.join(PROJECT_ROOT, 'data_cache')
        os.makedirs(cache_dir, exist_ok=True)
        factor_config_repr = json.dumps(self.config.factor_config, sort_keys=True)
        cache_key_parts = [self.CACHE_VERSION, start_date, end_date, '_'.join(sorted(self.symbols)), hashlib.md5(factor_config_repr.encode('utf-8')).hexdigest()]
        cache_file_path = os.path.join(cache_dir, "_".join(cache_key_parts) + ".pkl")

        if os.path.exists(cache_file_path):
            try:
                with open(cache_file_path, 'rb') as f: self.all_historical_data = pickle.load(f)
                logger.info(f"成功从缓存加载数据: {cache_file_path}")
                return
            except Exception as e:
                logger.warning(f"加载缓存失败: {e}. 将重新加载。")

        all_dfs = []
        base_path = getattr(self.config, 'local_minute_data_path', None)
        for symbol in self.symbols:
            df = load_local_minute_data(symbol, start_date, end_date, base_path)
            if df is not None and not df.empty:
                df = convert_ohlcv_to_uppercase(df)
                df_filled = df.fillna(method='ffill').fillna(method='bfill')
                df_with_factors = calculate_factors(df_filled, self.config.factor_config)
                df_with_factors['Symbol'] = symbol
                all_dfs.append(df_with_factors)
        
        if not all_dfs: raise ValueError("未能加载任何有效数据。")

        combined_data = pd.concat(all_dfs)
        self.all_historical_data = combined_data.set_index(['Symbol'], append=True).swaplevel(0, 1).sort_index()
        self.all_historical_data = self.all_historical_data.groupby(level='Symbol', group_keys=False).apply(lambda x: x.dropna())
        
        logger.info(f"数据加载和因子计算完成，总条数: {len(self.all_historical_data)}")
        with open(cache_file_path, 'wb') as f: pickle.dump(self.all_historical_data, f)
        logger.info(f"数据已成功缓存到: {cache_file_path}")

    def run_backtest(self, start_date: str, end_date: str):
        logger.info(f"--- 开始回测 ({self.strategy_instance.strategy_name}): {start_date} to {end_date} ---")
        self._load_data(start_date, end_date)
        
        all_timestamps = self.all_historical_data.index.get_level_values('Datetime').unique().sort_values()
        if all_timestamps.empty:
            logger.error("无有效时间戳，回测终止。")
            return None
        
        logger.info(f"共 {len(all_timestamps)} 个时间步长。")
        start_time = time.time()
        
        for i, current_timestamp in enumerate(all_timestamps):
            self.current_dt = current_timestamp
            if i > 0 and i % 10000 == 0: logger.info(f"回测进度: {i}/{len(all_timestamps)} ({current_timestamp})")

            try:
                current_bars_all_symbols = self.all_historical_data.loc[pd.IndexSlice[:, current_timestamp], :].reset_index(level='Datetime', drop=True)
                if current_bars_all_symbols.empty: continue
            except KeyError: continue
            
            self.portfolio.monitor_and_execute_sl_tp(current_bars_all_symbols['CLOSE'], self.current_dt)
            
            signals = self.strategy_instance.on_bar(current_bars_all_symbols)
            if signals:
                for signal in signals:
                    # 将 size 映射为 volume 以适配风险管理器
                    risk_check_params = signal.copy()
                    risk_check_params['volume'] = risk_check_params.pop('size')
                    if self.risk_manager.check_trade(**risk_check_params, portfolio=self.portfolio)[0]:
                        self.portfolio.execute_trade(signal)

            self._record_equity(current_bars_all_symbols)

        logger.info(f"回测主循环结束。总耗时: {time.time() - start_time:.2f}s")
        results = self._generate_results_metrics()
        self._plot_results(results)
        return results

    def _record_equity(self, current_prices_snapshot: pd.DataFrame):
        prices = current_prices_snapshot.get('CLOSE')
        if prices is None or prices.empty: return
        portfolio_value = self.portfolio.get_portfolio_value(prices)['total']
        self.equity_curve.append((self.current_dt, portfolio_value))
        
    def _generate_results_metrics(self) -> Optional[Dict[str, Any]]:
        if not self.equity_curve: return None
        equity_df = pd.DataFrame(self.equity_curve, columns=['timestamp', 'Equity']).set_index('timestamp')
        return calculate_backtest_metrics(equity_df, self.portfolio.trade_log, self.portfolio.initial_capital)

    def _plot_results(self, results: Optional[Dict]):
        if not results: return
        try:
            # 导入专业报告生成器
            from 专业回测报告图表模块 import ProfessionalBacktestReporter

            # 使用专业报告生成器
            reporter = ProfessionalBacktestReporter(self.strategy_instance.strategy_name)

            # 生成专业报告
            save_path = os.path.join(log_dir, f'专业回测报告_{self.strategy_instance.strategy_name}.png')
            reporter.create_professional_report(
                equity_curve=self.equity_curve,
                results=results,
                benchmark_data=None,  # 可以传入基准数据
                save_path=save_path
            )

            logger.info(f"专业回测报告已保存到 {save_path}")

        except Exception as e:
            logger.error(f"生成专业回测图表失败: {e}", exc_info=True)

            # 如果专业报告生成失败，回退到简单图表
            try:
                plt.style.use('seaborn-v0_8-whitegrid')
                fig = plt.figure(figsize=(15, 8))
                ax = fig.add_subplot(1, 1, 1)
                equity_df = pd.DataFrame(self.equity_curve, columns=['timestamp', 'Equity']).set_index('timestamp')
                ax.plot(equity_df.index, equity_df['Equity'], label='Strategy Equity', color='blue')
                ax.set_title(f"Backtest Equity Curve - {self.strategy_instance.strategy_name}", fontsize=16)
                ax.set_xlabel('Date'); ax.set_ylabel('Portfolio Value')
                ax.legend()
                plot_filename = os.path.join(log_dir, f'backtest_report_{self.strategy_instance.strategy_name}.png')
                plt.savefig(plot_filename)
                logger.info(f"简单回测报告图表已保存到 {plot_filename}")
                plt.close(fig)
            except Exception as e2:
                logger.error(f"生成简单回测图表也失败: {e2}", exc_info=True)

    def get_position_size(self, symbol: str) -> float:
        return self.portfolio.get_position_size(symbol)
        
    def get_portfolio_value(self, current_prices_snapshot: Optional[pd.Series] = None) -> Dict:
        return self.portfolio.get_portfolio_value(current_prices_snapshot)

# --- Main execution block ---
if __name__ == '__main__':
    if not imports_ok: sys.exit(1)
    
    parser = argparse.ArgumentParser(description="运行分钟线级别量化回测引擎 (V5 - 四大策略版)。")
    parser.add_argument('--start_date', type=str, default="2023-01-01", help='回测开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, default="2023-03-31", help='回测结束日期 (YYYY-MM-DD)')
    parser.add_argument('--strategy', type=str, default="AlphaXInspiredStrategy", help=f'要运行的策略名称，可选: {", ".join(STRATEGIES.keys())}')
    parser.add_argument('--symbols', type=str, default="BTCUSDT", help='交易标的，逗号分隔')
    parser.add_argument('--initial_cash', type=float, default=100000.0, help='初始资金')
    parser.add_argument('--local_data_path', type=str, default=r'D:\市场数据\现金\BTCUSDT', help='本地分钟数据路径')
    args = parser.parse_args()
    
    logger.info(f"--- 启动分钟级回测引擎 V5 (四大策略版) ---")
    logger.info(f"命令行参数: {args}")

    config = Config()
    config.start_date = args.start_date
    config.end_date = args.end_date
    config.initial_cash = args.initial_cash
    config.position_limit = 0.9
    config.local_minute_data_path = args.local_data_path
    config.transaction_cost_pct = 0.0005
    config.crypto_pairs = [s.strip().upper() for s in args.symbols.split(',')]
    config.benchmark_symbol = config.crypto_pairs[0] if config.crypto_pairs else 'BTCUSDT'
    config.min_trade_value_abs = 1000.0

    # 因子配置需要包含所有策略可能用到的所有因子
    config.factor_config = {
        'SMA_20': {'function': 'SMA', 'params': {'window': 20}},
        'SMA_60': {'function': 'SMA', 'params': {'window': 60}},
        'RSI_14': {'function': 'RSI', 'params': {'window': 14}},
        'ATR_14': {'function': 'ATR', 'params': {'window': 14}},
        'ADX_14': {'function': 'ADX', 'params': {'window': 14}},
        # 为均值回归策略添加因子
        'BBands_20_2_0': {'function': 'BBands', 'params': {'window': 20, 'num_std_dev': 2.0}},
    }

    strategy_class = STRATEGIES.get(args.strategy)
    if not strategy_class:
        logger.error(f"找不到策略: {args.strategy}. 可用: {list(STRATEGIES.keys())}")
        sys.exit(1)

    # 为不同策略设置不同的参数
    if args.strategy == "MeanReversion":
        strategy_parameters = {
            'bbands_period': 20, 'bbands_std_dev': 2.0,
            'rsi_period': 14, 'rsi_oversold': 30,
            'risk_per_trade_pct': 0.01, 'atr_sl_multiple': 1.5, 'atr_tp_multiple': 2.5,
        }
    elif args.strategy == "TrendFollowing":
        strategy_parameters = {
            'sma_short_period': 20, 'sma_long_period': 60,
            'adx_period': 14, 'adx_threshold': 25,
            'risk_per_trade_pct': 0.01, 'atr_sl_multiple': 2.0, 'atr_tp_multiple': 4.0,
        }
    elif args.strategy == "OptimizedTrendStrategyAI":
        strategy_parameters = {
            'model_path_prefix': 'BTCUSDT_random_forest', # 这是您AI模型文件的前缀
            'model_confidence_threshold': 0.65,
            'adx_threshold': 20,
            'risk_per_trade_pct': 0.015,
            'atr_sl_multiple': 1.8,
            'atr_tp_multiple': 3.5,
        }
    elif args.strategy == "OptimizedAlphaXStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20', 'sma_long_key': 'SMA_60',
            'adx_threshold': 30,  # 提高趋势要求
            'rsi_oversold': 25,   # 更严格的超卖条件
            'risk_per_trade_pct': 0.01,
            'atr_sl_multiple': 2.5,  # 增加止损距离
            'atr_tp_multiple': 5.0,  # 增加止盈距离
            'min_signal_interval_minutes': 240,  # 延长信号间隔
            'volume_threshold': 1.2,  # 成交量阈值
            'max_consecutive_losses': 3,  # 最大连续亏损次数
            'max_daily_trades': 2,  # 每日最大交易次数
            'price_near_sma_pct': 0.005,  # 价格接近均线的阈值
        }
    elif args.strategy == "EnhancedAlphaXStrategyV2":
        strategy_parameters = {
            'sma_short_key': 'SMA_10', 'sma_long_key': 'SMA_30',
            'adx_threshold': 20,  # 降低趋势要求
            'rsi_oversold': 30,   # 放宽超卖条件
            'rsi_overbought': 70, # 添加超买条件
            'risk_per_trade_pct': 0.02,  # 提高单笔风险
            'atr_sl_multiple': 2.0,  # 适中止损
            'atr_tp_multiple': 4.0,  # 适中止盈
            'min_signal_interval_minutes': 60,  # 缩短信号间隔
            'enable_short': True,  # 启用做空
            'volume_threshold': 1.1,  # 降低成交量要求
            'max_daily_trades': 4,  # 增加每日交易次数
        }
    elif args.strategy == "AIOptimizedAlphaXStrategy":
        strategy_parameters = {
            'model_path_prefix': 'BTCUSDT_random_forest',  # AI模型文件前缀
            'model_confidence_threshold': 0.6,  # AI预测置信度阈值
            'sma_short_key': 'SMA_20', 'sma_long_key': 'SMA_60',
            'adx_threshold': 20,  # 趋势强度要求
            'rsi_oversold': 30,   # RSI超卖阈值
            'rsi_overbought': 70, # RSI超买阈值
            'risk_per_trade_pct': 0.015,  # 单笔交易风险
            'atr_sl_multiple': 2.0,  # ATR止损倍数
            'atr_tp_multiple': 4.0,  # ATR止盈倍数
            'min_signal_interval_minutes': 120,  # 信号间隔
            'enable_short': False,  # 暂时禁用做空
            'max_daily_trades': 3,  # 每日最大交易次数
        }
    elif args.strategy == "SimplifiedAIAlphaXStrategy":
        strategy_parameters = {
            'confidence_threshold': 0.5,  # 智能评分阈值（降低以增加交易机会）
            'sma_short_key': 'SMA_20', 'sma_long_key': 'SMA_60',
            'adx_threshold': 15,  # 降低趋势要求
            'rsi_oversold': 35,   # 放宽RSI条件
            'rsi_overbought': 65, # 放宽RSI条件
            'risk_per_trade_pct': 0.015,  # 单笔交易风险
            'atr_sl_multiple': 2.0,  # ATR止损倍数
            'atr_tp_multiple': 4.0,  # ATR止盈倍数
            'min_signal_interval_minutes': 60,  # 缩短信号间隔
            'max_daily_trades': 4,  # 增加每日交易次数
        }
    elif args.strategy == "OptimizedMeanReversionStrategy":
        strategy_parameters = {
            'bbands_period': 30,  # 增加周期减少噪音
            'bbands_std_dev': 2.5,  # 更严格的超卖条件
            'rsi_period': 14,
            'rsi_oversold': 20,  # 更极端的超卖
            'adx_period': 14,
            'adx_max_threshold': 25,  # 震荡市过滤
            'risk_per_trade_pct': 0.005,  # 降低单笔风险
            'atr_sl_multiple': 2.0,  # 增加止损空间
            'atr_tp_multiple': 4.0,  # 提高盈亏比
            'min_signal_interval_minutes': 180,  # 3小时间隔
            'max_daily_trades': 3,  # 每日最多3次
            'volume_threshold': 1.2,  # 成交量确认
            'max_consecutive_losses': 3,  # 连续亏损保护
            'price_near_bb_pct': 0.002,  # 价格接近布林带下轨
        }
    elif args.strategy == "BalancedMeanReversionStrategy":
        strategy_parameters = {
            'bbands_period': 25,  # 适度增加周期
            'bbands_std_dev': 2.2,  # 适度严格
            'rsi_period': 14,
            'rsi_oversold': 25,  # 适度严格
            'adx_period': 14,
            'adx_max_threshold': 30,  # 放宽震荡市条件
            'risk_per_trade_pct': 0.008,  # 适度风险
            'atr_sl_multiple': 1.8,  # 适度止损
            'atr_tp_multiple': 3.5,  # 良好盈亏比
            'min_signal_interval_minutes': 120,  # 2小时间隔
            'max_daily_trades': 4,  # 每日最多4次
            'volume_threshold': 1.1,  # 降低成交量要求
            'max_consecutive_losses': 4,  # 适度保护
        }
    elif args.strategy == "SimpleMeanReversionStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'price_deviation_pct': 0.025,  # 价格偏离2.5%
            'rsi_key': 'RSI_14',
            'rsi_oversold': 30,  # RSI超卖
            'risk_per_trade_pct': 0.01,  # 单笔风险1%
            'atr_sl_multiple': 2.0,  # ATR止损倍数
            'atr_tp_multiple': 3.0,  # ATR止盈倍数
            'min_signal_interval_minutes': 120,  # 2小时间隔
            'max_daily_trades': 4,  # 每日最多4次
        }
    elif args.strategy == "EnhancedMeanReversionStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'price_deviation_pct': 0.02,  # 降低至2.0%
            'rsi_key': 'RSI_14',
            'rsi_oversold': 35,  # 放宽至35
            'risk_per_trade_pct': 0.008,  # 略微降低风险
            'atr_sl_multiple': 1.8,  # 缩小止损
            'atr_tp_multiple': 2.8,  # 缩小止盈，保持盈亏比
            'min_signal_interval_minutes': 90,  # 缩短至90分钟
            'max_daily_trades': 6,  # 增加至6次
            'volume_threshold': 1.05,  # 轻微成交量要求
            'trend_strength_threshold': 0.08,  # 放宽趋势强度
        }
    elif args.strategy == "AggressiveMeanReversionStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'price_deviation_pct': 0.015,  # 降低至1.5%
            'rsi_key': 'RSI_14',
            'rsi_oversold': 40,  # 大幅放宽至40
            'risk_per_trade_pct': 0.006,  # 降低单笔风险
            'atr_sl_multiple': 1.5,  # 更小止损
            'atr_tp_multiple': 2.5,  # 保持合理盈亏比
            'min_signal_interval_minutes': 60,  # 缩短至60分钟
            'max_daily_trades': 8,  # 增加至8次
        }
    elif args.strategy == "ProfitableMeanReversionStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'price_deviation_pct': 0.02,  # 2%偏离
            'rsi_key': 'RSI_14',
            'rsi_oversold': 30,  # 真正的超卖
            'risk_per_trade_pct': 0.005,  # 0.5%风险
            'atr_sl_multiple': 2.0,  # 给予充分空间
            'atr_tp_multiple': 4.0,  # 2:1盈亏比
            'min_signal_interval_minutes': 360,  # 6小时间隔
            'max_daily_trades': 2,  # 每日最多2次
            'volume_threshold': 1.2,  # 成交量确认
            'adx_max_threshold': 25,  # 震荡市过滤
            'min_price_level': 50000,  # 最低价格水平
        }
    elif args.strategy == "BalancedProfitableStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'price_deviation_pct': 0.015,  # 1.5%偏离
            'rsi_key': 'RSI_14',
            'rsi_oversold': 32,  # 适度超卖
            'risk_per_trade_pct': 0.008,  # 0.8%风险
            'atr_sl_multiple': 1.8,  # 适度止损
            'atr_tp_multiple': 3.6,  # 2:1盈亏比
            'min_signal_interval_minutes': 240,  # 4小时间隔
            'max_daily_trades': 2,  # 每日最多2次
            'volume_threshold': 1.1,  # 轻微成交量要求
            'adx_max_threshold': 30,  # 适度震荡市过滤
        }
    elif args.strategy == "PracticalMeanReversionStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'price_deviation_pct': 0.01,  # 1%偏离
            'risk_per_trade_pct': 0.01,  # 1%风险
            'atr_sl_multiple': 2.0,  # 止损倍数
            'atr_tp_multiple': 3.0,  # 止盈倍数
            'min_signal_interval_minutes': 360,  # 6小时间隔
            'max_daily_trades': 2,  # 每日最多2次
            'volume_threshold': 1.0,  # 成交量阈值
            'min_volatility_pct': 0.005,  # 最小波动率
        }
    elif args.strategy == "FinalProfitableStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'price_deviation_pct': 0.012,  # 提高至1.2%
            'risk_per_trade_pct': 0.008,  # 降低风险至0.8%
            'atr_sl_multiple': 1.8,  # 缩小止损
            'atr_tp_multiple': 3.6,  # 提高盈亏比至2:1
            'min_signal_interval_minutes': 480,  # 8小时间隔
            'max_daily_trades': 2,  # 每日最多2次
            'volume_threshold': 1.1,  # 轻微成交量要求
            'min_volatility_pct': 0.008,  # 提高波动率要求
            'trend_strength_threshold': 0.99,  # 更严格的趋势要求
        }
    elif args.strategy == "MonthlyProfitableStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'price_deviation_pct': 0.015,  # 1.5%偏离
            'risk_per_trade_pct': 0.005,  # 0.5%风险
            'atr_sl_multiple': 1.5,  # 严格止损
            'atr_tp_multiple': 3.5,  # 优秀盈亏比2.33:1
            'max_sl_pct': 0.02,  # 最大止损2%
            'max_tp_pct': 0.05,  # 最大止盈5%
            'min_signal_interval_minutes': 480,  # 8小时间隔
            'max_monthly_trades': 15,  # 每月最多15次
            'max_daily_trades': 1,  # 每日最多1次
            'min_volatility_pct': 0.008,  # 最小波动率0.8%
            'volume_threshold': 1.1,  # 成交量要求
            'trend_strength_threshold': 0.98,  # 趋势强度
            'price_position_threshold': 0.3,  # 价格位置
        }
    elif args.strategy == "UltraConservativeStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'price_deviation_pct': 0.025,  # 2.5%偏离（更严格）
            'risk_per_trade_pct': 0.003,  # 0.3%风险（更低）
            'atr_sl_multiple': 2.5,  # 更大止损空间
            'atr_tp_multiple': 5.0,  # 更高止盈目标
            'min_signal_interval_minutes': 1440,  # 24小时间隔
            'max_monthly_trades': 5,  # 每月最多5次
            'max_daily_trades': 1,  # 每日最多1次
            'min_volatility_pct': 0.015,  # 最小波动率1.5%
            'volume_threshold': 1.5,  # 更高成交量要求
            'trend_strength_threshold': 0.995,  # 极严格趋势要求
            'price_position_threshold': 0.2,  # 更严格价格位置
            'min_price_level': 60000,  # 最低价格水平
            'max_recent_volatility': 0.05,  # 最大近期波动率
        }
    elif args.strategy == "FixedMeanReversionStrategy":
        strategy_parameters = {
            'sma_short_key': 'SMA_20',
            'sma_long_key': 'SMA_60',
            'deviation_threshold': 0.005,  # 0.5%偏离
            'volatility_threshold': 0.003,  # 0.3%波动率
            'risk_per_trade_pct': 0.01,  # 1%风险
            'atr_sl_multiple': 2.0,
            'atr_tp_multiple': 1.5,  # 较低的盈亏比，模拟原版
            'min_signal_interval_minutes': 15,  # 15分钟间隔
            'max_daily_trades': 20,  # 每日最多20次
        }
    else: # 默认为 AlphaXInspiredStrategy 的参数
        strategy_parameters = {
            'sma_short_key': 'SMA_20', 'sma_long_key': 'SMA_60',
            'adx_threshold': 25, 'rsi_oversold': 35,
            'risk_per_trade_pct': 0.01, 'atr_sl_multiple': 2.0, 'atr_tp_multiple': 4.0,
            'min_signal_interval_minutes': 120,
        }

    backtester = MinuteEventBacktester(config, strategy_class, strategy_parameters)
    results = backtester.run_backtest(config.start_date, config.end_date)

    if results:
        logger.info(f"--- 回测性能报告 ({args.strategy}) ---")
        for key, value in results.items():
            if isinstance(value, float) and ('率' in key or '比' in key or '%' in key):
                logger.info(f"  {key}: {value:.2%}")
            elif isinstance(value, float):
                logger.info(f"  {key}: {value:,.4f}")
            else:
                logger.info(f"  {key}: {value}")
    else:
        logger.error("回测未产生有效结果。")