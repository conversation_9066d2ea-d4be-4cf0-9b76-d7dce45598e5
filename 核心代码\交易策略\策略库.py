# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import logging
from typing import Optional, Dict, List, Any, Tuple
import joblib
import os
import json

logger = logging.getLogger(__name__)

# --- 模型文件路径定义 ---
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
MODEL_DIR = os.path.join(PROJECT_ROOT, "模型文件")

# ==============================================================================
# --- 策略基类 ---
# ==============================================================================
class TradingStrategy:
    """
    事件驱动型交易策略的基类 (V3)。
    """
    def __init__(self, engine: Any, symbol_list: List[str], params: Optional[Dict[str, Any]] = None):
        self.engine = engine
        self.symbol_list = symbol_list
        self.parameters = params if params is not None else {}
        self.strategy_name = self.__class__.__name__
        logger.info(f"策略 {self.strategy_name} 初始化...")
        self.on_init()

    def on_init(self):
        pass

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        raise NotImplementedError("子类必须实现 on_bar 方法。")

    def _create_signal(self, **kwargs) -> Optional[Dict]:
        """
        创建一个标准化的交易信号字典。
        """
        size_abs = kwargs.get('size_abs')
        if size_abs is None or pd.isna(size_abs) or not isinstance(size_abs, (int, float, np.number)) or size_abs <= 1e-9:
            return None

        signal = {
            'symbol': kwargs.get('symbol'),
            'action': kwargs.get('action'),
            'price': float(kwargs.get('price')),
            'size': float(size_abs),
            'strategy': self.strategy_name,
            'timestamp': self.engine.current_dt,
            'signal_type': kwargs.get('signal_type'),
            'stop_loss_price': float(kwargs.get('stop_loss_price')) if kwargs.get('stop_loss_price') is not None and pd.notna(kwargs.get('stop_loss_price')) else None,
            'take_profit_price': float(kwargs.get('take_profit_price')) if kwargs.get('take_profit_price') is not None and pd.notna(kwargs.get('take_profit_price')) else None,
        }
        return signal

# ==============================================================================
# --- 策略1：MeanReversionStrategy (已修复并适配新框架) ---
# ==============================================================================
class MeanReversionStrategy(TradingStrategy):
    """
    均值回归策略 (修复版)。
    在布林带下轨和RSI超卖时买入。离场由统一的SL/TP机制处理。
    """
    def on_init(self):
        super().on_init()
        self.bbands_period = int(self.parameters.get('bbands_period', 20))
        self.bbands_std_dev = float(self.parameters.get('bbands_std_dev', 2.0))
        self.rsi_period = int(self.parameters.get('rsi_period', 14))
        self.rsi_oversold = float(self.parameters.get('rsi_oversold', 30.0))
        
        self.risk_per_trade_pct = float(self.parameters.get('risk_per_trade_pct', 0.01))
        self.atr_sl_multiple = float(self.parameters.get('atr_sl_multiple', 1.5))
        self.atr_tp_multiple = float(self.parameters.get('atr_tp_multiple', 2.5))
        
        self.bb_lower_key = f"BBands_{self.bbands_period}_{str(self.bbands_std_dev).replace('.', '_')}_BB_Lower"
        self.rsi_key = f"RSI_{self.rsi_period}"
        self.atr_key = 'ATR_14'

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index or abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol]
            price = data.get('CLOSE')
            bb_lower = data.get(self.bb_lower_key)
            rsi = data.get(self.rsi_key)

            if pd.isna(price) or pd.isna(bb_lower) or pd.isna(rsi): continue

            if price < bb_lower and rsi < self.rsi_oversold:
                atr = data.get(self.atr_key)
                if pd.isna(atr) or atr <= 0: continue

                stop_loss_price = price - atr * self.atr_sl_multiple
                take_profit_price = price + atr * self.atr_tp_multiple

                portfolio_value = self.engine.get_portfolio_value(data.to_frame().T)['total']
                size = self.engine.risk_manager.calculate_trade_size(portfolio_value, price, self.risk_per_trade_pct, stop_loss_price)

                signal = self._create_signal(
                    symbol=symbol, action='buy', price=price, size_abs=size,
                    signal_type='entry_mean_reversion',
                    stop_loss_price=stop_loss_price, take_profit_price=take_profit_price
                )
                if signal:
                    signals.append(signal)
                    logger.info(f"[{self.engine.current_dt}] {self.strategy_name}: {symbol} 买入信号. RSI={rsi:.2f}, Price<BB_Lower.")
        return signals

# ==============================================================================
# --- 策略2：TrendFollowingStrategy (已修复并适配新框架) ---
# ==============================================================================
class TrendFollowingStrategy(TradingStrategy):
    """
    趋势跟踪策略 (修复版)。
    """
    def on_init(self):
        super().on_init()
        self.sma_short_period = int(self.parameters.get('sma_short_period', 20))
        self.sma_long_period = int(self.parameters.get('sma_long_period', 60))
        self.adx_period = int(self.parameters.get('adx_period', 14))
        self.adx_threshold = float(self.parameters.get('adx_threshold', 25.0))
        self.risk_per_trade_pct = float(self.parameters.get('risk_per_trade_pct', 0.01))
        self.atr_sl_multiple = float(self.parameters.get('atr_sl_multiple', 2.0))
        self.atr_tp_multiple = float(self.parameters.get('atr_tp_multiple', 4.0))
        self.sma_short_key = f"SMA_{self.sma_short_period}"
        self.sma_long_key = f"SMA_{self.sma_long_period}"
        self.adx_key = f"ADX_{self.adx_period}"
        self.atr_key = 'ATR_14'

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index or abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue

            data = current_bar_data.loc[symbol]
            price, sma_short, sma_long, adx = data.get('CLOSE'), data.get(self.sma_short_key), data.get(self.sma_long_key), data.get(self.adx_key)

            if pd.isna(price) or pd.isna(sma_short) or pd.isna(sma_long) or pd.isna(adx): continue

            if sma_short > sma_long and adx > self.adx_threshold:
                atr = data.get(self.atr_key)
                if pd.isna(atr) or atr <= 0: continue

                stop_loss_price = price - atr * self.atr_sl_multiple
                take_profit_price = price + atr * self.atr_tp_multiple
                portfolio_value = self.engine.get_portfolio_value(data.to_frame().T)['total']
                size = self.engine.risk_manager.calculate_trade_size(portfolio_value, price, self.risk_per_trade_pct, stop_loss_price)

                signal = self._create_signal(
                    symbol=symbol, action='buy', price=price, size_abs=size,
                    signal_type='entry_trend_following',
                    stop_loss_price=stop_loss_price, take_profit_price=take_profit_price
                )
                if signal:
                    signals.append(signal)
                    logger.info(f"[{self.engine.current_dt}] {self.strategy_name}: {symbol} 买入信号. SMA金叉, ADX={adx:.2f}.")
        return signals

# ==============================================================================
# --- 策略3：OptimizedTrendStrategyAI (已修复并适配新框架) ---
# ==============================================================================
class OptimizedTrendStrategyAI(TradingStrategy):
    """
    优化趋势AI策略 (加固版)。
    """
    _model = None
    _scaler = None
    _feature_list = None

    def on_init(self):
        super().on_init()
        self.model_path_prefix = self.parameters.get('model_path_prefix')
        self.model_confidence_threshold = float(self.parameters.get('model_confidence_threshold', 0.6))
        self.adx_key = self.parameters.get('adx_key', 'ADX_14')
        self.adx_threshold = float(self.parameters.get('adx_threshold', 20.0))
        self.risk_per_trade_pct = float(self.parameters.get('risk_per_trade_pct', 0.015))
        self.atr_sl_multiple = float(self.parameters.get('atr_sl_multiple', 1.8))
        self.atr_tp_multiple = float(self.parameters.get('atr_tp_multiple', 3.5))
        self.atr_key = 'ATR_14'
        self._load_model_assets()
        logger.info(f"{self.strategy_name}: 初始化完成。AI模型: {'已加载' if self._model else '未加载'}.")

    def _load_model_assets(self):
        if not self.model_path_prefix:
            logger.warning(f"[{self.strategy_name}] 未提供 'model_path_prefix' 参数，AI功能禁用。")
            return
        try:
            self._model = joblib.load(os.path.join(MODEL_DIR, f"{self.model_path_prefix}_model.joblib"))
            self._scaler = joblib.load(os.path.join(MODEL_DIR, f"{self.model_path_prefix}_scaler.joblib"))
            with open(os.path.join(MODEL_DIR, f"{self.model_path_prefix}_features.json"), 'r') as f:
                self._feature_list = json.load(f)
            logger.info(f"[{self.strategy_name}] AI模型及相关文件加载成功 for '{self.model_path_prefix}'.")
        except Exception as e:
            logger.error(f"[{self.strategy_name}] 加载AI模型时出错: {e}", exc_info=True)
            self._model = None

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index or abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol]
            prediction, confidence = self._get_ai_prediction(data)
            adx = data.get(self.adx_key, 0)

            if prediction == 1 and confidence >= self.model_confidence_threshold and adx > self.adx_threshold:
                price, atr = data.get('CLOSE'), data.get(self.atr_key)
                if pd.isna(price) or pd.isna(atr) or price <= 0 or atr <= 0: continue
                stop_loss_price = price - atr * self.atr_sl_multiple
                take_profit_price = price + atr * self.atr_tp_multiple
                portfolio_value = self.engine.get_portfolio_value(data.to_frame().T)['total']
                size = self.engine.risk_manager.calculate_trade_size(portfolio_value, price, self.risk_per_trade_pct, stop_loss_price)
                signal = self._create_signal(symbol=symbol, action='buy', price=price, size_abs=size, signal_type='entry_trend_ai', stop_loss_price=stop_loss_price, take_profit_price=take_profit_price)
                if signal:
                    signals.append(signal)
                    logger.info(f"[{self.engine.current_dt}] {self.strategy_name}: {symbol} AI买入信号. Conf={confidence:.2f}, ADX={adx:.2f}.")
        return signals

    def _get_ai_prediction(self, data: pd.Series) -> Tuple[Optional[int], float]:
        if not all([self._model, self._scaler, self._feature_list]): return None, 0.0
        try:
            features_df = data[self._feature_list].to_frame().T
            if features_df.isnull().values.any(): return None, 0.0
            scaled_features = self._scaler.transform(features_df)
            probabilities = self._model.predict_proba(scaled_features)[0]
            return (1, probabilities[1]) if probabilities[1] >= self.model_confidence_threshold else (0, probabilities[1])
        except Exception:
            return None, 0.0

# ==============================================================================
# --- 策略4：AlphaXInspiredStrategy (作为标杆和最佳实践) ---
# ==============================================================================
class AlphaXInspiredStrategy(TradingStrategy):
    """
    AlphaX风格策略 (V2.1 - 稳健版)
    """
    def on_init(self):
        super().on_init()
        self.sma_short_key = self.parameters.get('sma_short_key', 'SMA_20')
        self.sma_long_key = self.parameters.get('sma_long_key', 'SMA_60')
        self.adx_key = self.parameters.get('adx_key', 'ADX_14')
        self.adx_threshold = float(self.parameters.get('adx_threshold', 25.0))
        self.rsi_key = self.parameters.get('rsi_key', 'RSI_14')
        self.rsi_oversold = float(self.parameters.get('rsi_oversold', 35.0))
        self.risk_per_trade_pct = float(self.parameters.get('risk_per_trade_pct', 0.01))
        self.atr_sl_multiple = float(self.parameters.get('atr_sl_multiple', 2.0))
        self.atr_tp_multiple = float(self.parameters.get('atr_tp_multiple', 4.0))
        self.atr_key = 'ATR_14'
        self.min_signal_interval_minutes = int(self.parameters.get('min_signal_interval_minutes', 120))
        self.last_signal_time: Dict[str, pd.Timestamp] = {}
        logger.info(f"{self.strategy_name}: 初始化完成。")

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index or abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            if symbol in self.last_signal_time and (self.engine.current_dt - self.last_signal_time[symbol]).total_seconds() / 60 < self.min_signal_interval_minutes:
                continue
            
            data = current_bar_data.loc[symbol]
            if self._is_strong_uptrend(data) and self._is_buy_trigger(data):
                entry_package = self._calculate_entry_package(symbol, data)
                if entry_package:
                    signal = self._create_signal(**entry_package)
                    if signal:
                        signals.append(signal)
                        self.last_signal_time[symbol] = self.engine.current_dt
                        logger.info(f"[{self.engine.current_dt}] {self.strategy_name}: 为 {symbol} 生成买入信号, 数量={signal['size']:.4f}")
        return signals

    def _is_strong_uptrend(self, data: pd.Series) -> bool:
        vals = [data.get(k) for k in [self.sma_short_key, self.sma_long_key, self.adx_key]]
        return not any(pd.isna(v) for v in vals) and vals[0] > vals[1] and vals[2] > self.adx_threshold

    def _is_buy_trigger(self, data: pd.Series) -> bool:
        rsi = data.get(self.rsi_key)
        return not pd.isna(rsi) and rsi < self.rsi_oversold

    def _calculate_entry_package(self, symbol: str, data: pd.Series) -> Optional[Dict]:
        price, atr = data.get('CLOSE'), data.get(self.atr_key)
        if pd.isna(price) or pd.isna(atr) or price <= 0 or atr <= 0: return None
        stop_loss_price = price - atr * self.atr_sl_multiple
        take_profit_price = price + atr * self.atr_tp_multiple
        portfolio_value = self.engine.get_portfolio_value(data.to_frame().T)['total']
        size_abs = self.engine.risk_manager.calculate_trade_size(portfolio_value, price, self.risk_per_trade_pct, stop_loss_price)
        if size_abs is None or size_abs <= 1e-9: return None
        return {'symbol': symbol, 'action': 'buy', 'price': price, 'size_abs': size_abs, 'signal_type': 'entry_alphax_inspired', 'stop_loss_price': stop_loss_price, 'take_profit_price': take_profit_price}

# --- 策略字典 ---
STRATEGIES = {
    'MeanReversion': MeanReversionStrategy,
    'TrendFollowing': TrendFollowingStrategy,
    'OptimizedTrendStrategyAI': OptimizedTrendStrategyAI,
    'AlphaXInspiredStrategy': AlphaXInspiredStrategy,
    'OptimizedAlphaXStrategy': None,  # 将在导入后更新
}

# 优化策略导入
from .optimized_alphax_strategy import OptimizedAlphaXStrategy

# 更新策略字典
STRATEGIES['OptimizedAlphaXStrategy'] = OptimizedAlphaXStrategy

# 更新策略映射字典
STRATEGY_MAP = {
    'AlphaXInspiredStrategy': AlphaXInspiredStrategy,
    'TrendFollowingStrategy': TrendFollowingStrategy,
    'MeanReversionStrategy': MeanReversionStrategy,
    'OptimizedAlphaXStrategy': OptimizedAlphaXStrategy,  # 新增优化策略
}


# 增强策略V2导入
from .enhanced_alphax_strategy_v2 import EnhancedAlphaXStrategyV2

# 更新策略字典
STRATEGIES['EnhancedAlphaXStrategyV2'] = EnhancedAlphaXStrategyV2


# AI优化策略导入
from .ai_optimized_alphax_strategy import AIOptimizedAlphaXStrategy

# 更新策略字典
STRATEGIES['AIOptimizedAlphaXStrategy'] = AIOptimizedAlphaXStrategy


# 简化版AI策略导入
from .simplified_ai_alphax_strategy import SimplifiedAIAlphaXStrategy

# 更新策略字典
STRATEGIES['SimplifiedAIAlphaXStrategy'] = SimplifiedAIAlphaXStrategy


# 优化均值回归策略导入
from .optimized_mean_reversion_strategy import OptimizedMeanReversionStrategy

# 更新策略字典
STRATEGIES['OptimizedMeanReversionStrategy'] = OptimizedMeanReversionStrategy


# 平衡均值回归策略导入
from .balanced_mean_reversion_strategy import BalancedMeanReversionStrategy

# 更新策略字典
STRATEGIES['BalancedMeanReversionStrategy'] = BalancedMeanReversionStrategy


# 简化均值回归策略导入
from .simple_mean_reversion_strategy import SimpleMeanReversionStrategy

# 更新策略字典
STRATEGIES['SimpleMeanReversionStrategy'] = SimpleMeanReversionStrategy


# 增强均值回归策略导入
from .enhanced_mean_reversion_strategy import EnhancedMeanReversionStrategy

# 更新策略字典
STRATEGIES['EnhancedMeanReversionStrategy'] = EnhancedMeanReversionStrategy


# 激进均值回归策略导入
from .aggressive_mean_reversion_strategy import AggressiveMeanReversionStrategy

# 更新策略字典
STRATEGIES['AggressiveMeanReversionStrategy'] = AggressiveMeanReversionStrategy


# 盈利均值回归策略导入
from .profitable_mean_reversion_strategy import ProfitableMeanReversionStrategy

# 更新策略字典
STRATEGIES['ProfitableMeanReversionStrategy'] = ProfitableMeanReversionStrategy


# 平衡盈利策略导入
from .balanced_profitable_strategy import BalancedProfitableStrategy

# 更新策略字典
STRATEGIES['BalancedProfitableStrategy'] = BalancedProfitableStrategy


# 实用均值回归策略导入
from .practical_mean_reversion_strategy import PracticalMeanReversionStrategy

# 更新策略字典
STRATEGIES['PracticalMeanReversionStrategy'] = PracticalMeanReversionStrategy


# 最终盈利策略导入
from .final_profitable_strategy import FinalProfitableStrategy

# 更新策略字典
STRATEGIES['FinalProfitableStrategy'] = FinalProfitableStrategy


# 月月盈利策略导入
from .monthly_profitable_strategy import MonthlyProfitableStrategy

# 更新策略字典
STRATEGIES['MonthlyProfitableStrategy'] = MonthlyProfitableStrategy


# 超保守策略导入
from .ultra_conservative_strategy import UltraConservativeStrategy

# 更新策略字典
STRATEGIES['UltraConservativeStrategy'] = UltraConservativeStrategy
